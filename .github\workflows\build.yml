name: Build Frontend

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:  

permissions:
  contents: write

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install frontend dependencies
      run: |
        cd frontend
        npm ci
    
    - name: Build frontend
      run: |
        cd frontend
        npm run build
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: frontend-dist
        path: frontend/dist/
        retention-days: 30
    
    - name: Commit and push build
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add frontend/dist/ -f
        git diff --staged --quiet || git commit -m "Auto-build frontend [skip ci]"
        git push
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}