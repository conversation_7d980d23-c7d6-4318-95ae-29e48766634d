# Colab AI UI 🚀

Proyecto completo de React + FastAPI listo para ejecutar en Google Colab con una sola línea de código.

## ¿Qué hace este proyecto?

- **Frontend**: Interfaz React minimalista que se comunica con el backend
- **Backend**: Servidor FastAPI que sirve la UI y proporciona API endpoints
- **Auto-build**: GitHub Actions compila automáticamente el frontend
- **Túnel público**: Cloudflared expone la UI para acceso externo desde Colab
- **Una sola línea**: Solo ejecuta una celda en Colab y todo funciona

## 🎯 Uso en Google Colab

### Opción 1: Clonar y ejecutar
```python
!git clone https://github.com/jaumejp/colab-ai-ui.git
%cd colab-ai-ui
!python launch_colab.py