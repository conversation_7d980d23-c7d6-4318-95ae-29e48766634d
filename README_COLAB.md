# Colab AI UI - Guía para Google Colab

Esta guía te ayudará a ejecutar la aplicación Colab AI UI directamente en Google Colab.

## 🚀 Inicio Rápido

Ejecuta estos comandos en una celda de Google Colab:

```python
# Clonar el repositorio
!git clone https://github.com/jaumejp/colab-ai-ui.git

# Cambiar al directorio del proyecto
%cd colab-ai-ui

# Ejecutar el script de lanzamiento
!python launch_colab.py
```

## ✨ ¿Qué hace el script?

El script `launch_colab.py` automáticamente:

1. **Instala dependencias de Python** desde `requirements.txt`
2. **Verifica el build del frontend** (compilado por GitHub Actions)
3. **Instala cloudflared** para crear túneles públicos
4. **Inicia el servidor backend** (FastAPI que sirve tanto API como frontend)
5. **Crea túnel público** para acceder desde fuera de Colab
6. **Integra frontend y backend** en una sola URL

## 🌐 Acceso a la aplicación

Una vez ejecutado el script, verás algo como:

```
============================================================
🚀 Colab AI UI está corriendo!
------------------------------------------------------------
Aplicación completa: https://abc123.trycloudflare.com
Frontend y Backend integrados en una sola URL
------------------------------------------------------------
Endpoints disponibles:
  - API: https://abc123.trycloudflare.com/api/hello
  - Frontend: https://abc123.trycloudflare.com/
============================================================
```

**Usa la URL principal** para acceder a la aplicación completa.

## 🔧 Características

- **Frontend React** con Tailwind CSS compilado y optimizado
- **Backend FastAPI** que sirve tanto API como frontend
- **Túnel público** automático con cloudflared
- **Integración completa** en una sola URL
- **Build optimizado** generado por GitHub Actions

## 🛠️ Desarrollo

Si quieres hacer cambios al código:

1. **Modifica los archivos** en Colab usando el editor de archivos
2. **Los cambios se reflejan automáticamente** gracias al hot reload
3. **El frontend se actualiza** automáticamente al guardar cambios

## 📁 Estructura del proyecto

```
colab-ai-ui/
├── frontend/          # Aplicación React
│   ├── src/
│   ├── package.json
│   └── tailwind.config.js
├── backend/           # API FastAPI
│   ├── main.py
│   └── requirements.txt
├── launch_colab.py    # Script de lanzamiento para Colab
└── README_COLAB.md    # Esta guía
```

## ⚠️ Notas importantes

- **Los túneles son temporales** - se crean nuevas URLs cada vez que ejecutas el script
- **Guarda tu trabajo** - los cambios en Colab se pierden al cerrar la sesión
- **Tiempo de ejecución** - Colab tiene límites de tiempo, guarda tu progreso regularmente

## 🐛 Solución de problemas

Si algo no funciona:

1. **Reinicia el runtime** de Colab
2. **Ejecuta de nuevo** los comandos
3. **Verifica las URLs** - pueden cambiar en cada ejecución
4. **Revisa los logs** para errores específicos

## 📝 Próximos pasos

Una vez que la aplicación esté corriendo:

1. Accede a la URL del Frontend
2. Explora la interfaz React con Tailwind CSS
3. Prueba las funcionalidades de IA
4. Modifica el código según tus necesidades

¡Disfruta desarrollando con Colab AI UI! 🎉
