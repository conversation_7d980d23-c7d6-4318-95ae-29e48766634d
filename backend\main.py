from fastapi import FastAPI, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
import uvicorn
from pathlib import Path

app = FastAPI(title="Colab UI Backend", version="1.0.0")

# Configurar ruta de archivos estáticos
FRONTEND_DIR = Path(__file__).parent.parent / "frontend" / "dist"

# ================================
# API ENDPOINTS (siempre JSON)
# ================================

@app.get("/api/hello")
async def hello():
    """Endpoint de prueba que devuelve saludo"""
    return {
        "message": "¡Hola desde FastAPI en Colab! 🚀",
        "status": "success"
    }

# ================================
# STATIC FILE SERVING
# ================================

# Servir archivos estáticos del frontend si existe
if FRONTEND_DIR.exists():
    # Montar carpeta de assets (CSS, JS, imágenes)
    assets_dir = FRONTEND_DIR / "assets"
    if assets_dir.exists():
        app.mount("/assets", StaticFiles(directory=assets_dir), name="static")
    
    @app.get("/{path:path}")
    async def serve_spa(path: str = ""):
        """
        Servir la SPA de React para todas las rutas que no sean API.
        Esto permite que React Router funcione correctamente.
        """
        # Las rutas API ya están manejadas arriba
        
        # Para la raíz o cualquier ruta de frontend, servir index.html
        index_file = FRONTEND_DIR / "index.html"
        if index_file.exists():
            return FileResponse(index_file)
        else:
            raise HTTPException(
                status_code=500,
                detail="Frontend index.html not found"
            )

else:
    # Si no existe el frontend, servir mensaje de error para rutas no-API
    @app.get("/")
    async def no_frontend_root():
        """Página de error cuando no hay frontend compilado"""
        return FileResponse(
            Path(__file__).parent / "no_frontend.html"
        ) if (Path(__file__).parent / "no_frontend.html").exists() else {
            "error": "Frontend not available",
            "message": "Frontend build not found. Run GitHub Actions to build React app.",
            "api_available": True,
            "try": "/api/status"
        }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")