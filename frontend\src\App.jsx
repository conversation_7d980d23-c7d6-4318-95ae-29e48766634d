"use client"

import { useState } from "react"

export default function App() {
  const [message, setMessage] = useState("")
  const [loading, setLoading] = useState(false)
  const [apiData, setApiData] = useState(null)

  const handleTestConnection = async () => {
    setLoading(true)
    try {
      const response = await fetch("/api/hello")
      const data = await response.json()
      setMessage(data.message)
      setApiData(data)
    } catch (error) {
      setMessage("Error: No se pudo conectar con el backend")
      setApiData({ error: true, message: error.message })
      console.error("Error:", error)
    }
    setLoading(false)
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <header className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-5xl font-bold text-gray-900 mb-4">
            <span className="text-orange-500">Colab UI</span> Demo
          </h1>
        </div>
      </header>

      <main className="flex-1 container mx-auto px-4">
        <div className="flex justify-center mb-8">
          <button
            onClick={handleTestConnection}
            disabled={loading}
            className="bg-white border-2 border-gray-300 hover:border-orange-500 hover:text-orange-500 hover:shadow-md
                     disabled:border-gray-200 disabled:text-gray-400 disabled:cursor-not-allowed
                     text-gray-800 font-semibold py-4 px-8 rounded-lg cursor-pointer
                     transition-all duration-200 shadow-sm"
          >
            {loading ? (
              <span className="flex items-center">
                <svg
                  className="animate-spin -ml-1 mr-3 h-5 w-5"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Cargando...
              </span>
            ) : (
              "Probar Conexión"
            )}
          </button>
        </div>

        {message && (
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-xl border-2 border-gray-200 shadow-lg p-6 mb-6">
              <div className="flex items-center mb-4">
                <div className={`w-3 h-3 rounded-full mr-3 ${apiData?.error ? "bg-red-500" : "bg-orange-500"}`}></div>
                <h3 className="text-xl font-semibold text-gray-900">Respuesta del Servidor</h3>
              </div>

              <div className="bg-gray-100 rounded-lg p-4 border-2 border-gray-200">
                <p className={`text-lg font-medium mb-3 ${apiData?.error ? "text-red-600" : "text-orange-600"}`}>
                  {message}
                </p>

                {apiData && (
                  <div className="space-y-2">
                    <div className="text-sm text-gray-600 mb-2 font-medium">Datos JSON:</div>
                    <pre className="bg-white rounded-md p-3 text-sm text-gray-800 overflow-x-auto border-2 border-gray-200 shadow-sm">
                      {JSON.stringify(apiData, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </main>

      <footer className="container mx-auto px-4 py-8">
        <div className="text-center pt-8 border-t-2 border-gray-200">
          <p className="text-gray-600 text-sm">
            Built by Jaume Juan
          </p>
        </div>
      </footer>
    </div>
  )
}
