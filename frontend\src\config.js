// Configuración para conectar con el backend
const config = {
  // En desarrollo local
  API_BASE_URL: import.meta.env.VITE_API_URL || 'http://localhost:8000',
  
  // Detectar si estamos en Colab
  isColab: () => {
    return window.location.hostname.includes('colab.research.google.com') ||
           window.location.hostname.includes('trycloudflare.com');
  },
  
  // Obtener la URL base de la API
  getApiUrl: () => {
    // Si hay una variable de entorno específica, usarla
    if (import.meta.env.VITE_API_URL) {
      return import.meta.env.VITE_API_URL;
    }
    
    // Si estamos en Colab, intentar detectar la URL del backend
    if (config.isColab()) {
      // En Colab, el backend debería estar en un puerto diferente o URL diferente
      // Por ahora, usar localhost:8000 y que cloudflared maneje el túnel
      return 'http://localhost:8000';
    }
    
    // Por defecto, desarrollo local
    return 'http://localhost:8000';
  }
};

export default config;
