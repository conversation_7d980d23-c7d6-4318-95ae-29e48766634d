import os
import sys
import subprocess
import time
import threading
import requests
import zipfile
from pathlib import Path
import webbrowser
import json

def install_requirements():
    print("Instalando dependencias de Python...")
    subprocess.run([
        sys.executable, "-m", "pip", "install", "-r", "requirements.txt", "-q"
    ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)

def install_nodejs():
    """Instala Node.js y npm en Colab"""
    print("Instalando Node.js...")
    try:
        # Verificar si Node.js ya está instalado
        result = subprocess.run(["node", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"Node.js ya está instalado: {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass

    try:
        # Instalar Node.js usando apt
        subprocess.run(["apt", "update", "-qq"], check=True, stdout=subprocess.DEVNULL)
        subprocess.run([
            "apt", "install", "-y", "-qq", "nodejs", "npm"
        ], check=True, stdout=subprocess.DEVNULL)

        # Verificar instalación
        result = subprocess.run(["node", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"Node.js instalado correctamente: {result.stdout.strip()}")
            return True
        else:
            print("❌ Error: No se pudo verificar la instalación de Node.js")
            return False

    except subprocess.CalledProcessError as e:
        print(f"❌ Error instalando Node.js: {e}")
        return False

def install_frontend_dependencies():
    """Instala las dependencias del frontend React"""
    print("Instalando dependencias del frontend...")
    try:
        os.chdir("frontend")

        # Instalar dependencias
        subprocess.run([
            "npm", "install"
        ], check=True, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)

        print("Dependencias del frontend instaladas ✓")
        os.chdir("..")  # Volver al directorio raíz
        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ Error instalando dependencias del frontend: {e}")
        os.chdir("..")  # Asegurar que volvemos al directorio raíz
        return False

def configure_frontend_env(backend_url):
    """Configura las variables de entorno del frontend"""
    try:
        env_content = f"VITE_API_URL={backend_url}\n"

        with open("frontend/.env", "w") as f:
            f.write(env_content)

        print(f"Frontend configurado para usar backend: {backend_url}")
        return True

    except Exception as e:
        print(f"⚠️  Error configurando variables de entorno: {e}")
        return False


def install_cloudflared():
    """Instala cloudflared para crear túneles públicos"""
    print("Instalando cloudflared...")
    try:
        # Descargar e instalar cloudflared
        subprocess.run([
            "wget", "-q", "https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-amd64",
            "-O", "cloudflared"
        ], check=True, stdout=subprocess.DEVNULL)
        
        subprocess.run(["chmod", "+x", "cloudflared"], check=True)
        return True
    except subprocess.CalledProcessError:
        print("⚠️  No se pudo instalar cloudflared, usando modo local")
        return False

def start_backend():
    """Inicia el servidor FastAPI en segundo plano"""
    print("Iniciando servidor FastAPI...")

    def run_server():
        original_dir = os.getcwd()
        os.chdir("backend")
        subprocess.run([sys.executable, "main.py"], stdout=subprocess.DEVNULL)
        os.chdir(original_dir)

    thread = threading.Thread(target=run_server, daemon=True)
    thread.start()

    # Esperar a que el servidor esté listo
    for _ in range(30):  # 30 segundos máximo
        try:
            response = requests.get("http://localhost:8000/api/status", timeout=2)
            if response.status_code == 200:
                print("Servidor FastAPI iniciado ✓")
                return True
        except:
            time.sleep(1)

    print("❌ Error: No se pudo iniciar el servidor FastAPI")
    return False

def start_frontend():
    """Inicia el servidor de React en segundo plano"""
    print("Iniciando servidor React...")

    def run_frontend():
        original_dir = os.getcwd()
        os.chdir("frontend")
        # Usar npm run dev para iniciar el servidor de desarrollo
        subprocess.run(["npm", "run", "dev"], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        os.chdir(original_dir)

    thread = threading.Thread(target=run_frontend, daemon=True)
    thread.start()

    # Esperar a que el servidor esté listo
    for _ in range(60):  # 60 segundos máximo (React puede tardar más)
        try:
            response = requests.get("http://localhost:5173", timeout=2)
            if response.status_code == 200:
                print("Servidor React iniciado ✓")
                return True
        except:
            time.sleep(1)

    print("❌ Error: No se pudo iniciar el servidor React")
    return False

def start_tunnel_backend():
    """Inicia cloudflared para crear túnel público del backend"""
    if not Path("cloudflared").exists():
        print("Backend ejecutando en modo local: http://localhost:8000")
        return "http://localhost:8000"

    print("Creando túnel público para backend...")

    try:
        process = subprocess.Popen([
            "./cloudflared", "tunnel", "--url", "http://localhost:8000"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

        # Leer la URL del túnel
        for _ in range(30):  # 30 segundos máximo
            if process.poll() is not None:
                break

            line = process.stderr.readline()
            if "trycloudflare.com" in line:
                url = line.split("https://")[1].split()[0]
                return f"https://{url}"
            time.sleep(1)

    except Exception as e:
        print(f"⚠️  Error con cloudflared para backend: {e}")

    return "http://localhost:8000"

def start_tunnel_frontend():
    """Inicia cloudflared para crear túnel público del frontend"""
    if not Path("cloudflared").exists():
        print("Frontend ejecutando en modo local: http://localhost:5173")
        return "http://localhost:5173"

    print("Creando túnel público para frontend...")

    try:
        # Crear un segundo proceso de cloudflared para el frontend
        process = subprocess.Popen([
            "./cloudflared", "tunnel", "--url", "http://localhost:5173"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

        # Leer la URL del túnel
        for _ in range(30):  # 30 segundos máximo
            if process.poll() is not None:
                break

            line = process.stderr.readline()
            if "trycloudflare.com" in line:
                url = line.split("https://")[1].split()[0]
                return f"https://{url}"
            time.sleep(1)

    except Exception as e:
        print(f"⚠️  Error con cloudflared para frontend: {e}")

    return "http://localhost:5173"

def main():
    """Función principal"""
    print("Iniciando Colab AI UI...")

    # 1. Instalar dependencias de Python
    install_requirements()

    # 2. Instalar Node.js
    if not install_nodejs():
        print("❌ Error: No se pudo instalar Node.js")
        return

    # 3. Instalar dependencias del frontend
    if not install_frontend_dependencies():
        print("❌ Error: No se pudieron instalar las dependencias del frontend")
        return

    # 4. Instalar cloudflared
    cloudflared_available = install_cloudflared()

    # 5. Iniciar backend
    if not start_backend():
        return

    # 6. Iniciar frontend
    if not start_frontend():
        return

    # 7. Crear túneles públicos o usar local
    if cloudflared_available:
        backend_url = start_tunnel_backend()
        # Configurar el frontend con la URL del backend
        configure_frontend_env(backend_url)
        frontend_url = start_tunnel_frontend()
    else:
        backend_url = "http://localhost:8000"
        # Configurar el frontend con la URL local del backend
        configure_frontend_env(backend_url)
        frontend_url = "http://localhost:5173"

    # 8. Mostrar información y abrir en nueva pestaña
    print("\n" + "="*70)
    print("🚀 Colab AI UI está corriendo!")
    print("-" * 70)
    print(f"Frontend (React): {frontend_url}")
    print(f"Backend (API):    {backend_url}")
    print("-" * 70)
    print("Usa la URL del Frontend para acceder a la aplicación")
    print("="*70 + "\n")

    # Intentar abrir automáticamente el frontend (funciona en algunos entornos de Colab)
    try:
        webbrowser.open(frontend_url)
    except:
        pass

    # Mantener el script corriendo
    try:
        print("Presiona Ctrl+C para detener los servidores...")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("Cerrando servidores...")

if __name__ == "__main__":
    main()