import os
import sys
import subprocess
import time
import threading
import requests
import zipfile
from pathlib import Path
import webbrowser
import json

def install_requirements():
    print("Instalando dependencias de Python...")
    subprocess.run([
        sys.executable, "-m", "pip", "install", "-r", "requirements.txt", "-q"
    ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)

def check_frontend_build():
    """Verifica si existe el build del frontend"""
    frontend_dist = Path("frontend/dist")
    if frontend_dist.exists():
        print("Frontend build encontrado ✓")
        return True
    else:
        print("⚠️  Frontend build no encontrado")
        print("   El backend funcionará solo como API")
        return False


def install_cloudflared():
    """Instala cloudflared para crear túneles públicos"""
    print("Instalando cloudflared...")
    try:
        # Descargar e instalar cloudflared
        subprocess.run([
            "wget", "-q", "https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-amd64",
            "-O", "cloudflared"
        ], check=True, stdout=subprocess.DEVNULL)
        
        subprocess.run(["chmod", "+x", "cloudflared"], check=True)
        return True
    except subprocess.CalledProcessError:
        print("⚠️  No se pudo instalar cloudflared, usando modo local")
        return False

def start_backend():
    """Inicia el servidor FastAPI en segundo plano"""
    print("Iniciando servidor FastAPI...")

    def run_server():
        original_dir = os.getcwd()
        os.chdir("backend")
        subprocess.run([sys.executable, "main.py"], stdout=subprocess.DEVNULL)
        os.chdir(original_dir)

    thread = threading.Thread(target=run_server, daemon=True)
    thread.start()

    # Esperar a que el servidor esté listo
    for _ in range(30):  # 30 segundos máximo
        try:
            response = requests.get("http://localhost:8000/api/status", timeout=2)
            if response.status_code == 200:
                print("Servidor FastAPI iniciado ✓")
                return True
        except:
            time.sleep(1)

    print("❌ Error: No se pudo iniciar el servidor FastAPI")
    return False



def start_tunnel():
    """Inicia cloudflared para crear túnel público"""
    if not Path("cloudflared").exists():
        print("Ejecutando en modo local: http://localhost:8000")
        return "http://localhost:8000"

    print("Creando túnel público...")

    try:
        process = subprocess.Popen([
            "./cloudflared", "tunnel", "--url", "http://localhost:8000"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

        # Leer la URL del túnel
        for _ in range(30):  # 30 segundos máximo
            if process.poll() is not None:
                break

            line = process.stderr.readline()
            if "trycloudflare.com" in line:
                url = line.split("https://")[1].split()[0]
                return f"https://{url}"
            time.sleep(1)

    except Exception as e:
        print(f"⚠️  Error con cloudflared: {e}")

    return "http://localhost:8000"

def main():
    """Función principal"""
    print("Iniciando Colab AI UI...")

    # 1. Instalar dependencias de Python
    install_requirements()

    # 2. Verificar si existe el build del frontend
    frontend_available = check_frontend_build()

    # 3. Instalar cloudflared
    cloudflared_available = install_cloudflared()

    # 4. Iniciar backend (que servirá tanto API como frontend si existe)
    if not start_backend():
        return

    # 5. Crear túnel público o usar local
    if cloudflared_available:
        url = start_tunnel()
    else:
        url = "http://localhost:8000"

    # 6. Mostrar información y abrir en nueva pestaña
    print("\n" + "="*60)
    print("🚀 Colab AI UI está corriendo!")
    print("-" * 60)
    if frontend_available:
        print(f"Aplicación completa: {url}")
        print("Frontend y Backend integrados en una sola URL")
    else:
        print(f"Solo API Backend: {url}")
        print("Frontend build no encontrado")
    print("-" * 60)
    print("Endpoints disponibles:")
    print(f"  - API: {url}/api/hello")
    if frontend_available:
        print(f"  - Frontend: {url}/")
    print("="*60 + "\n")

    # Intentar abrir automáticamente (funciona en algunos entornos de Colab)
    try:
        webbrowser.open(url)
    except:
        pass

    # Mantener el script corriendo
    try:
        print("Presiona Ctrl+C para detener el servidor...")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("Cerrando servidor...")

if __name__ == "__main__":
    main()